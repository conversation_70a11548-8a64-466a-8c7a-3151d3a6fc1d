import React, { useState } from 'react';
import type { Model } from '../types';
import { saveModels } from '../utils/storage';

interface ModelManagerProps {
  models: Model[];
  onModelsChange: (models: Model[]) => void;
}

const ModelManager: React.FC<ModelManagerProps> = ({
  models,
  onModelsChange
}) => {
  const [isAdding, setIsAdding] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    provider: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (editingId) {
      // Edit existing model
      const updatedModels = models.map(m => 
        m.id === editingId 
          ? { ...m, ...formData }
          : m
      );
      onModelsChange(updatedModels);
      saveModels(updatedModels);
      setEditingId(null);
    } else {
      // Add new model
      const newModel: Model = {
        id: Date.now().toString(),
        ...formData
      };
      const updatedModels = [...models, newModel];
      onModelsChange(updatedModels);
      saveModels(updatedModels);
      setIsAdding(false);
    }
    
    setFormData({ name: '', provider: '' });
  };

  const handleEdit = (model: Model) => {
    setFormData({
      name: model.name,
      provider: model.provider
    });
    setEditingId(model.id);
    setIsAdding(true);
  };

  const handleDelete = (id: string) => {
    if (confirm('Are you sure you want to delete this model?')) {
      const updatedModels = models.filter(m => m.id !== id);
      onModelsChange(updatedModels);
      saveModels(updatedModels);
    }
  };

  const handleCancel = () => {
    setIsAdding(false);
    setEditingId(null);
    setFormData({ name: '', provider: '' });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold gradient-text float-animation">
          🤖 Model Management
        </h2>
        <button
          onClick={() => setIsAdding(true)}
          className="btn-animated bg-gradient-to-r from-orange-500 to-red-500 text-white px-6 py-3 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
        >
          ✨ Add Model
        </button>
      </div>

      {isAdding && (
        <div className="glass-effect rounded-xl p-6 card-animated">
          <h3 className="text-xl font-semibold mb-4 text-white">
            {editingId ? '✏️ Edit Model' : '➕ Add New Model'}
          </h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-white font-medium mb-2">
                Model Name
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="e.g., GPT-4, Claude 3.5 Sonnet"
                className="w-full px-4 py-3 rounded-lg bg-white/20 text-white placeholder-white/70 border border-white/30 focus:border-white/60 focus:outline-none transition-all duration-300"
                required
              />
            </div>
            <div>
              <label className="block text-white font-medium mb-2">
                Provider
              </label>
              <input
                type="text"
                value={formData.provider}
                onChange={(e) => setFormData({ ...formData, provider: e.target.value })}
                placeholder="e.g., OpenAI, Anthropic, Google"
                className="w-full px-4 py-3 rounded-lg bg-white/20 text-white placeholder-white/70 border border-white/30 focus:border-white/60 focus:outline-none transition-all duration-300"
                required
              />
            </div>
            <div className="flex gap-3">
              <button
                type="submit"
                className="btn-animated bg-gradient-to-r from-green-500 to-blue-500 text-white px-6 py-3 rounded-lg font-semibold flex-1"
              >
                {editingId ? '💾 Update Model' : '✅ Add Model'}
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="btn-animated bg-gradient-to-r from-gray-500 to-gray-600 text-white px-6 py-3 rounded-lg font-semibold"
              >
                ❌ Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {models.map((model, index) => (
          <div
            key={model.id}
            className="glass-effect rounded-xl p-6 card-animated hover:scale-105 transition-all duration-300"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className="flex justify-between items-start mb-4">
              <div className="text-3xl">🤖</div>
              <div className="flex gap-2">
                <button
                  onClick={() => handleEdit(model)}
                  className="btn-animated bg-gradient-to-r from-blue-500 to-purple-500 text-white px-3 py-2 rounded-lg text-sm hover:shadow-lg"
                >
                  ✏️
                </button>
                <button
                  onClick={() => handleDelete(model.id)}
                  className="btn-animated bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-2 rounded-lg text-sm hover:shadow-lg"
                >
                  🗑️
                </button>
              </div>
            </div>

            <h4 className="text-xl font-bold text-white mb-2">
              {model.name}
            </h4>
            <p className="text-white/80 text-sm">
              {model.provider}
            </p>
          </div>
        ))}
      </div>

      {models.length === 0 && !isAdding && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4 bounce-animation">🤖</div>
          <h3 className="text-2xl font-bold text-white mb-2">No Models Yet</h3>
          <p className="text-white/70 mb-6">Add your first AI model to start testing!</p>
          <button
            onClick={() => setIsAdding(true)}
            className="btn-animated bg-gradient-to-r from-orange-500 to-red-500 text-white px-8 py-4 rounded-lg font-semibold text-lg"
          >
            🚀 Add First Model
          </button>
        </div>
      )}
    </div>
  );
};

export default ModelManager;
