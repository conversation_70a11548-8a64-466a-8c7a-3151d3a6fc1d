import React, { useState } from 'react';
import type { Model } from '../types';
import { saveModels } from '../utils/storage';

interface ModelManagerProps {
  models: Model[];
  onModelsChange: (models: Model[]) => void;
}

const ModelManager: React.FC<ModelManagerProps> = ({
  models,
  onModelsChange
}) => {
  const [isAdding, setIsAdding] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    provider: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (editingId) {
      // Edit existing model
      const updatedModels = models.map(m => 
        m.id === editingId 
          ? { ...m, ...formData }
          : m
      );
      onModelsChange(updatedModels);
      saveModels(updatedModels);
      setEditingId(null);
    } else {
      // Add new model
      const newModel: Model = {
        id: Date.now().toString(),
        ...formData
      };
      const updatedModels = [...models, newModel];
      onModelsChange(updatedModels);
      saveModels(updatedModels);
      setIsAdding(false);
    }
    
    setFormData({ name: '', provider: '' });
  };

  const handleEdit = (model: Model) => {
    setFormData({
      name: model.name,
      provider: model.provider
    });
    setEditingId(model.id);
    setIsAdding(true);
  };

  const handleDelete = (id: string) => {
    if (confirm('Are you sure you want to delete this model?')) {
      const updatedModels = models.filter(m => m.id !== id);
      onModelsChange(updatedModels);
      saveModels(updatedModels);
    }
  };

  const handleCancel = () => {
    setIsAdding(false);
    setEditingId(null);
    setFormData({ name: '', provider: '' });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Model Management</h2>
        <button
          onClick={() => setIsAdding(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Add Model
        </button>
      </div>

      {isAdding && (
        <div className="bg-white p-6 rounded-lg shadow border">
          <h3 className="text-lg font-semibold mb-4">
            {editingId ? 'Edit Model' : 'Add New Model'}
          </h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Model Name
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="e.g., GPT-4, Claude 3.5 Sonnet"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Provider
              </label>
              <input
                type="text"
                value={formData.provider}
                onChange={(e) => setFormData({ ...formData, provider: e.target.value })}
                placeholder="e.g., OpenAI, Anthropic, Google"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            <div className="flex space-x-3">
              <button
                type="submit"
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                {editingId ? 'Update' : 'Add'} Model
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Models ({models.length})
          </h3>
        </div>
        <div className="divide-y divide-gray-200">
          {models.map((model) => (
            <div key={model.id} className="p-6">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="text-lg font-medium text-gray-900">
                    {model.name}
                  </h4>
                  <p className="text-gray-600">{model.provider}</p>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleEdit(model)}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleDelete(model.id)}
                    className="text-red-600 hover:text-red-800 text-sm font-medium"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}
          {models.length === 0 && (
            <div className="p-6 text-center text-gray-500">
              No models yet. Add your first model to get started!
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ModelManager;
