export interface Category {
  id: string;
  name: string;
  color: string;
  description?: string;
}

export interface Question {
  id: string;
  title: string;
  description: string;
  category: string;
}

export interface Model {
  id: string;
  name: string;
  provider: string;
}

export interface TestResult {
  questionId: string;
  modelId: string;
  status: 'passed' | 'failed' | 'pending';
}

export interface ModelScore {
  modelId: string;
  modelName: string;
  provider: string;
  totalQuestions: number;
  passedQuestions: number;
  failedQuestions: number;
  pendingQuestions: number;
  passRate: number;
}
