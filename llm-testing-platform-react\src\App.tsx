import React, { useState, useEffect } from 'react';
import type { Question, Model, TestResult, ModelScore } from './types';
import { getQuestions, getModels, getTestResults, saveTestResults } from './utils/storage';
import QuestionManager from './components/QuestionManager';
import ModelManager from './components/ModelManager';
import TestingGrid from './components/TestingGrid';
import Leaderboard from './components/Leaderboard';

function App() {
  const [questions, setQuestions] = useState<Question[]>([]);
  const [models, setModels] = useState<Model[]>([]);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [activeTab, setActiveTab] = useState<'testing' | 'questions' | 'models' | 'leaderboard'>('testing');

  useEffect(() => {
    setQuestions(getQuestions());
    setModels(getModels());
    setTestResults(getTestResults());
  }, []);

  const updateTestResult = (questionId: string, modelId: string, status: 'passed' | 'failed' | 'pending') => {
    const newResults = testResults.filter(r => !(r.questionId === questionId && r.modelId === modelId));
    newResults.push({ questionId, modelId, status });
    setTestResults(newResults);
    saveTestResults(newResults);
  };

  const calculateLeaderboard = (): ModelScore[] => {
    return models.map(model => {
      const modelResults = testResults.filter(r => r.modelId === model.id);
      const passed = modelResults.filter(r => r.status === 'passed').length;
      const failed = modelResults.filter(r => r.status === 'failed').length;
      const pending = questions.length - passed - failed;
      
      return {
        modelId: model.id,
        modelName: model.name,
        provider: model.provider,
        totalQuestions: questions.length,
        passedQuestions: passed,
        failedQuestions: failed,
        pendingQuestions: pending,
        passRate: questions.length > 0 ? (passed / questions.length) * 100 : 0
      };
    }).sort((a, b) => b.passRate - a.passRate);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-blue-600 text-white p-4">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-2xl font-bold mb-4">AI Test v2 🤖</h1>
          <nav className="flex space-x-4">
            <button
              onClick={() => setActiveTab('testing')}
              className={`px-4 py-2 rounded ${activeTab === 'testing' ? 'bg-blue-800' : 'bg-blue-500 hover:bg-blue-700'}`}
            >
              Testing Grid
            </button>
            <button
              onClick={() => setActiveTab('questions')}
              className={`px-4 py-2 rounded ${activeTab === 'questions' ? 'bg-blue-800' : 'bg-blue-500 hover:bg-blue-700'}`}
            >
              Questions
            </button>
            <button
              onClick={() => setActiveTab('models')}
              className={`px-4 py-2 rounded ${activeTab === 'models' ? 'bg-blue-800' : 'bg-blue-500 hover:bg-blue-700'}`}
            >
              Models
            </button>
            <button
              onClick={() => setActiveTab('leaderboard')}
              className={`px-4 py-2 rounded ${activeTab === 'leaderboard' ? 'bg-blue-800' : 'bg-blue-500 hover:bg-blue-700'}`}
            >
              Leaderboard
            </button>
          </nav>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6">
        {activeTab === 'testing' && (
          <TestingGrid
            questions={questions}
            models={models}
            testResults={testResults}
            onUpdateResult={updateTestResult}
          />
        )}
        {activeTab === 'questions' && (
          <QuestionManager
            questions={questions}
            onQuestionsChange={setQuestions}
          />
        )}
        {activeTab === 'models' && (
          <ModelManager
            models={models}
            onModelsChange={setModels}
          />
        )}
        {activeTab === 'leaderboard' && (
          <Leaderboard scores={calculateLeaderboard()} />
        )}
      </div>
    </div>
  );
}

export default App;
