import React from 'react';
import type { Question, Model, TestResult } from '../types';

interface TestingGridProps {
  questions: Question[];
  models: Model[];
  testResults: TestResult[];
  onUpdateResult: (questionId: string, modelId: string, status: 'passed' | 'failed' | 'pending') => void;
}

const TestingGrid: React.FC<TestingGridProps> = ({
  questions,
  models,
  testResults,
  onUpdateResult
}) => {
  const getResultStatus = (questionId: string, modelId: string): 'passed' | 'failed' | 'pending' => {
    const result = testResults.find(r => r.questionId === questionId && r.modelId === modelId);
    return result?.status || 'pending';
  };

  const getStatusColor = (status: 'passed' | 'failed' | 'pending'): string => {
    switch (status) {
      case 'passed': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const getStatusText = (status: 'passed' | 'failed' | 'pending'): string => {
    switch (status) {
      case 'passed': return 'Passed';
      case 'failed': return 'Failed';
      default: return '-';
    }
  };

  if (questions.length === 0 || models.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 text-lg">
          {questions.length === 0 ? 'No questions available. ' : ''}
          {models.length === 0 ? 'No models available. ' : ''}
          Please add some {questions.length === 0 ? 'questions' : ''} 
          {questions.length === 0 && models.length === 0 ? ' and ' : ''}
          {models.length === 0 ? 'models' : ''} to start testing.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-80">
                Questions
              </th>
              {models.map(model => (
                <th key={model.id} className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-32">
                  <div className="flex flex-col">
                    <span className="font-semibold">{model.name}</span>
                    <span className="text-gray-400 text-xs">{model.provider}</span>
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {questions.map((question, questionIndex) => (
              <tr key={question.id} className={questionIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                <td className="px-6 py-4 text-sm">
                  <div>
                    <div className="font-medium text-gray-900 mb-1">{question.title}</div>
                    <div className="text-gray-600 text-xs mb-2">{question.description}</div>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {question.category}
                    </span>
                  </div>
                </td>
                {models.map(model => {
                  const status = getResultStatus(question.id, model.id);
                  return (
                    <td key={model.id} className="px-3 py-4 text-center">
                      <select
                        value={status}
                        onChange={(e) => onUpdateResult(question.id, model.id, e.target.value as 'passed' | 'failed' | 'pending')}
                        className={`w-full px-2 py-1 text-xs rounded border-0 ${getStatusColor(status)} cursor-pointer focus:ring-2 focus:ring-blue-500`}
                      >
                        <option value="pending">-</option>
                        <option value="passed">Passed</option>
                        <option value="failed">Failed</option>
                      </select>
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TestingGrid;
