import React, { useState, useEffect } from 'react';
import type { Question, Model, TestResult, Category } from '../types';
import { getCategories } from '../utils/storage';

interface TestingGridProps {
  questions: Question[];
  models: Model[];
  testResults: TestResult[];
  onUpdateResult: (questionId: string, modelId: string, status: 'passed' | 'failed' | 'pending') => void;
}

const TestingGrid: React.FC<TestingGridProps> = ({
  questions,
  models,
  testResults,
  onUpdateResult
}) => {
  const [categories, setCategories] = useState<Category[]>([]);

  useEffect(() => {
    setCategories(getCategories());
  }, []);

  const getResultStatus = (questionId: string, modelId: string): 'passed' | 'failed' | 'pending' => {
    const result = testResults.find(r => r.questionId === questionId && r.modelId === modelId);
    return result?.status || 'pending';
  };

  const getStatusColor = (status: 'passed' | 'failed' | 'pending'): string => {
    switch (status) {
      case 'passed': return 'status-passed text-white';
      case 'failed': return 'status-failed text-white';
      default: return 'bg-gradient-to-r from-gray-400 to-gray-500 text-white';
    }
  };

  const getStatusText = (status: 'passed' | 'failed' | 'pending'): string => {
    switch (status) {
      case 'passed': return 'Passed';
      case 'failed': return 'Failed';
      default: return '-';
    }
  };

  if (questions.length === 0 || models.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4 bounce-animation">🧪</div>
        <h3 className="text-2xl font-bold text-white mb-2">Ready to Start Testing?</h3>
        <p className="text-white/70 text-lg mb-6">
          {questions.length === 0 ? 'No questions available. ' : ''}
          {models.length === 0 ? 'No models available. ' : ''}
          Please add some {questions.length === 0 ? 'questions' : ''}
          {questions.length === 0 && models.length === 0 ? ' and ' : ''}
          {models.length === 0 ? 'models' : ''} to start testing.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-3xl font-bold gradient-text float-animation">
        🧪 Testing Grid
      </h2>

      <div className="glass-effect rounded-xl overflow-hidden card-animated">
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-gradient-to-r from-purple-600/20 to-blue-600/20">
              <tr>
                <th className="px-6 py-4 text-left text-sm font-bold text-white uppercase tracking-wider w-80">
                  📝 Questions
                </th>
                {models.map((model, index) => (
                  <th key={model.id} className="px-3 py-4 text-center text-sm font-bold text-white uppercase tracking-wider min-w-32">
                    <div className="flex flex-col card-animated" style={{ animationDelay: `${index * 0.1}s` }}>
                      <span className="font-bold">{model.name}</span>
                      <span className="text-white/70 text-xs">{model.provider}</span>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-white/10">
              {questions.map((question, questionIndex) => {
                const category = categories.find(cat => cat.name === question.category);
                return (
                  <tr key={question.id} className="hover:bg-white/5 transition-all duration-300">
                    <td className="px-6 py-6 text-sm">
                      <div className="card-animated" style={{ animationDelay: `${questionIndex * 0.1}s` }}>
                        <div className="font-bold text-white mb-2 text-lg">{question.title}</div>
                        <div className="text-white/80 text-sm mb-3 leading-relaxed">{question.description}</div>
                        <div className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full pulse-animation"
                            style={{ backgroundColor: category?.color || '#667eea' }}
                          />
                          <span className="text-white/90 text-xs font-medium px-3 py-1 rounded-full bg-white/10">
                            {question.category}
                          </span>
                        </div>
                      </div>
                    </td>
                    {models.map((model, modelIndex) => {
                      const status = getResultStatus(question.id, model.id);
                      return (
                        <td key={model.id} className="px-3 py-6 text-center">
                          <div className="card-animated" style={{ animationDelay: `${(questionIndex + modelIndex) * 0.05}s` }}>
                            <select
                              value={status}
                              onChange={(e) => onUpdateResult(question.id, model.id, e.target.value as 'passed' | 'failed' | 'pending')}
                              className={`w-full px-3 py-2 text-sm rounded-lg border-0 ${getStatusColor(status)} cursor-pointer focus:ring-2 focus:ring-white/50 transition-all duration-300 font-semibold`}
                            >
                              <option value="pending" className="bg-gray-800 text-white">⏳ Pending</option>
                              <option value="passed" className="bg-gray-800 text-white">✅ Passed</option>
                              <option value="failed" className="bg-gray-800 text-white">❌ Failed</option>
                            </select>
                          </div>
                        </td>
                      );
                    })}
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default TestingGrid;
