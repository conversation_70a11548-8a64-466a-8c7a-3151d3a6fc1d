import type { Question, Model, TestResult } from '../types';

const STORAGE_KEYS = {
  QUESTIONS: 'llm-testing-questions',
  MODELS: 'llm-testing-models',
  RESULTS: 'llm-testing-results',
};

// Questions
export const getQuestions = (): Question[] => {
  const stored = localStorage.getItem(STORAGE_KEYS.QUESTIONS);
  return stored ? JSON.parse(stored) : getDefaultQuestions();
};

export const saveQuestions = (questions: Question[]): void => {
  localStorage.setItem(STORAGE_KEYS.QUESTIONS, JSON.stringify(questions));
};

// Models
export const getModels = (): Model[] => {
  const stored = localStorage.getItem(STORAGE_KEYS.MODELS);
  return stored ? JSON.parse(stored) : getDefaultModels();
};

export const saveModels = (models: Model[]): void => {
  localStorage.setItem(STORAGE_KEYS.MODELS, JSON.stringify(models));
};

// Test Results
export const getTestResults = (): TestResult[] => {
  const stored = localStorage.getItem(STORAGE_KEYS.RESULTS);
  return stored ? JSON.parse(stored) : [];
};

export const saveTestResults = (results: TestResult[]): void => {
  localStorage.setItem(STORAGE_KEYS.RESULTS, JSON.stringify(results));
};

// Default data
const getDefaultQuestions = (): Question[] => [
  {
    id: '1',
    title: 'Coding Challenge',
    description: 'Write a function that reverses a string without using built-in reverse methods',
    category: 'CODING'
  },
  {
    id: '2',
    title: 'Logic Problem',
    description: 'Solve this logic puzzle: If all roses are flowers and some flowers fade quickly...',
    category: 'SUBJECTS'
  },
  {
    id: '3',
    title: 'Creative Writing',
    description: 'Write a short story about a time traveler who gets stuck in a loop',
    category: 'SUBJECTS'
  }
];

const getDefaultModels = (): Model[] => [
  { id: '1', name: 'Claude 4 Sonnet', provider: 'Anthropic' },
  { id: '2', name: 'Claude 4 Sonnet Thinking', provider: 'Anthropic' },
  { id: '3', name: 'Claude 4 Opus', provider: 'Anthropic' },
  { id: '4', name: 'ChatGPT 4o', provider: 'OpenAI' },
  { id: '5', name: 'Gemini 2.0 Pro', provider: 'Google' }
];
